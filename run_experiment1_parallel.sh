#!/bin/bash

# 实验一并行运行脚本
# 在2个GPU上并行运行llama-3.2-1b和llama-3.2-3b模型的实验

set -e

echo "=========================================="
echo "实验一：2个模型并行运行"
echo "模型: llama-3.2-1b, llama-3.2-3b"
echo "Type01模式: 1, 2, 3"
echo "=========================================="

# 检查GPU状态
echo "检查GPU状态..."
nvidia-smi

echo ""
echo "开始并行实验..."

# 运行并行实验
python3 experiment1_parallel.py

echo ""
echo "实验完成！"
echo "结果保存在 error/result-acc-ppl/ 目录下"

# 显示结果文件
echo ""
echo "生成的结果文件："
ls -la error/result-acc-ppl/*_results_type*_*.json 2>/dev/null || echo "暂无结果文件"
