#!/usr/bin/env python3
"""
实验一并行版本：在2个GPU上并行运行2个模型的实验
优化版本，专门针对实验一的需求设计
"""

import os
import json
import torch
import multiprocessing as mp
import gc
from datetime import datetime
from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight, error_inject_activation

# 确保结果目录存在
os.makedirs("error/result-acc-ppl", exist_ok=True)

def run_model_experiments(model_info, gpu_id):
    """在指定GPU上运行单个模型的所有type01实验"""
    model_dir, model_name = model_info
    
    # 设置使用的GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: 开始运行 {model_name} 的所有实验")
    
    # 实验参数
    error_rate_list = [0.0000000000001, 0.0000000000005, 0.000000000001, 0.000000000005, 
                       0.00000000001, 0.00000000005, 0.0000000001, 0.0000000005, 
                       0.000000001, 0.000000005, 0.00000001, 0.00000005, 
                       0.0000001, 0.0000005, 0.000001]
    
    # 运行3种type01模式的实验
    for type01 in [1, 2, 3]:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: {model_name} 开始 type01={type01} 实验")
        
        # 根据type01确定bit_range_list
        if type01 == 3:
            bit_range_list = [2, 4, 6, 8, 10, 12, 14, 16]
        else:  # type01 = 1 或 2
            bit_range_list = [2, 4, 6, 8, 10, 12, 14, 15]
        
        # 结果保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = f"error/result-acc-ppl/{model_name}_results_type{type01}_{timestamp}.json"
        results = []
        
        total_iterations = len(error_rate_list) * len(bit_range_list)
        current_iteration = 0
        
        for error_rate in error_rate_list:
            for bit_range in bit_range_list:
                current_iteration += 1
                print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: {model_name} type01={type01}, error_rate: {error_rate}, bit_range: {bit_range} ({current_iteration}/{total_iterations})")
                
                try:
                    # 重新加载模型以确保每次实验都是从干净状态开始
                    model, tokenizer = load_llama_model_and_tokenizer(model_dir)
                    model = error_inject_weight(model, error_rate, bit_range, type01)
                    hooks = error_inject_activation(model, error_rate, bit_range, type01)
                    
                    accuracy, ppl = eval_model(model, tokenizer)
                    result_entry = {
                        "model": model_name,
                        "error_rate": error_rate,
                        "bit_range": bit_range,
                        "type": type01,
                        "acc": accuracy,
                        "ppl": ppl
                    }
                    results.append(result_entry)
                    
                    # 每次迭代后保存结果，防止中途中断丢失数据
                    with open(save_path, "w", encoding="utf-8") as f:
                        json.dump(results, f, ensure_ascii=False, indent=2)
                        
                except Exception as e:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: 错误发生在 {model_name}, type01={type01}, error_rate={error_rate}, bit_range={bit_range}: {str(e)}")
                
                finally:
                    # 清理资源
                    if 'hooks' in locals():
                        for hook in hooks:
                            hook.remove()
                    if 'model' in locals():
                        del model
                    if 'tokenizer' in locals():
                        del tokenizer
                    gc.collect()
                    torch.cuda.empty_cache()
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: {model_name} type01={type01} 实验完成！结果保存到: {save_path}")
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: {model_name} 所有实验完成！")

def main():
    """主函数"""
    # 为保证CUDA在多进程中的稳定性，建议使用'spawn'模式
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass

    # 检查可用的GPU数量
    gpu_count = torch.cuda.device_count()
    print(f"检测到GPU数量: {gpu_count}")
    
    if gpu_count == 0:
        print("错误：没有可用的GPU。请检查您的环境。")
        return
    elif gpu_count < 2:
        print(f"警告: 只有 {gpu_count} 个GPU可用，将使用单GPU模式运行。")
        use_gpu_count = 1
    else:
        # 使用2个GPU进行并行
        use_gpu_count = 2
        print(f"将使用 {use_gpu_count} 个GPU进行并行实验")
    
    # 实验一的模型信息: (模型路径, 模型名称)
    model_infos = [
        ("/root/lanyun-tmp/models/llama-3.2-1b", "llama-3.2-1b"),
        ("/root/lanyun-tmp/models/llama-3.2-3b", "llama-3.2-3b")
    ]
    
    print("========================================")
    print("实验一：2个模型并行运行")
    print(f"模型: {[info[1] for info in model_infos]}")
    print(f"Type01模式: [1, 2, 3]")
    print(f"使用GPU: {list(range(use_gpu_count))}")
    print("========================================")
    
    # 创建任务列表
    tasks = []
    
    if use_gpu_count >= 2:
        # 2个GPU的情况：每个GPU运行一个模型的所有实验
        for i, model_info in enumerate(model_infos):
            gpu_id = i % use_gpu_count
            tasks.append((model_info, gpu_id))
    else:
        # 1个GPU的情况：顺序运行所有模型
        for model_info in model_infos:
            tasks.append((model_info, 0))
    
    print(f"任务分配:")
    for i, (model_info, gpu_id) in enumerate(tasks):
        print(f"  GPU {gpu_id}: {model_info[1]} (所有type01实验)")
    
    start_time = datetime.now()
    print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 使用进程池管理并发执行的任务
    with mp.Pool(processes=use_gpu_count) as pool:
        pool.starmap(run_model_experiments, tasks)
    
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\n========================================")
    print(f"所有实验完成！")
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {duration}")
    print(f"结果保存在 error/result-acc-ppl/ 目录下")
    print("========================================")

if __name__ == "__main__":
    main()
