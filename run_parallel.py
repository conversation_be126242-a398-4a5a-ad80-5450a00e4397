import os
import json
import torch
import multiprocessing as mp
import gc
from datetime import datetime
from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight, error_inject_activation

# 确保结果目录存在
os.makedirs("error/result-acc-ppl", exist_ok=True)

def run_experiment(model_info, gpu_id, type01):
    """在指定GPU上运行单个模型的实验"""
    model_dir, model_name = model_info

    # 设置使用的GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 进程 {os.getpid()} 在 GPU {gpu_id} 上运行 {model_name} 实验 (type01={type01})")

    # 实验参数
    error_rate_list = [0.0000000000001, 0.0000000000005, 0.000000000001, 0.000000000005,
                       0.00000000001, 0.00000000005, 0.0000000001, 0.0000000005,
                       0.000000001, 0.000000005, 0.00000001, 0.00000005,
                       0.0000001, 0.0000005, 0.000001]

    # 根据type01确定bit_range_list
    if type01 == 3:
        bit_range_list = [2, 4, 6, 8, 10, 12, 14, 16]
    else:  # type01 = 1 或 2
        bit_range_list = [2, 4, 6, 8, 10, 12, 14, 15]

    # 结果保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = f"error/result-acc-ppl/{model_name}_results_type{type01}_{timestamp}.json"
    results = []

    total_iterations = len(error_rate_list) * len(bit_range_list)
    current_iteration = 0

    print(f"[{datetime.now().strftime('%H:%M:%S')}] 开始加载模型 {model_name}...")

    for error_rate in error_rate_list:
        for bit_range in bit_range_list:
            current_iteration += 1
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {model_name} - type01={type01}, error_rate: {error_rate}, bit_range: {bit_range} ({current_iteration}/{total_iterations})")

            try:
                # 重新加载模型以确保每次实验都是从干净状态开始
                model, tokenizer = load_llama_model_and_tokenizer(model_dir)
                model = error_inject_weight(model, error_rate, bit_range, type01)
                hooks = error_inject_activation(model, error_rate, bit_range, type01)

                accuracy, ppl = eval_model(model, tokenizer)
                result_entry = {
                    "model": model_name,
                    "error_rate": error_rate,
                    "bit_range": bit_range,
                    "type": type01,
                    "acc": accuracy,
                    "ppl": ppl
                }
                results.append(result_entry)

                # 每次迭代后保存结果，防止中途中断丢失数据
                with open(save_path, "w", encoding="utf-8") as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)

            except Exception as e:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 错误发生在 {model_name}, type01={type01}, error_rate={error_rate}, bit_range={bit_range}: {str(e)}")

            finally:
                # 清理资源
                if 'hooks' in locals():
                    for hook in hooks:
                        hook.remove()
                if 'model' in locals():
                    del model
                if 'tokenizer' in locals():
                    del tokenizer
                gc.collect()
                torch.cuda.empty_cache()

    print(f"[{datetime.now().strftime('%H:%M:%S')}] {model_name} type01={type01} 实验完成！结果保存到: {save_path}")

if __name__ == "__main__":
    # 为保证CUDA在多进程中的稳定性，建议使用'spawn'模式
    # 这行代码需要在任何与multiprocessing相关的代码之前
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass

    # 检查可用的GPU数量
    gpu_count = torch.cuda.device_count()
    print(f"检测到GPU数量: {gpu_count}")

    if gpu_count == 0:
        print("错误：没有可用的GPU。请检查您的环境。")
        exit()
    elif gpu_count < 2:
        print(f"警告: 只有 {gpu_count} 个GPU可用，建议至少2个GPU以获得最佳并行效果。")
        gpu_count = 1
    else:
        # 使用2个GPU进行并行
        gpu_count = min(gpu_count, 2)
        print(f"将使用 {gpu_count} 个GPU进行并行实验")

    # 实验一的模型信息: (模型路径, 模型名称)
    model_infos = [
        ("/root/lanyun-tmp/models/llama-3.2-1b", "llama-3.2-1b"),
        ("/root/lanyun-tmp/models/llama-3.2-3b", "llama-3.2-3b")
    ]

    print("========================================")
    print("实验一：2个模型并行运行")
    print(f"模型: {[info[1] for info in model_infos]}")
    print(f"Type01模式: [1, 2, 3]")
    print(f"使用GPU: {list(range(gpu_count))}")
    print("========================================")

    # 创建任务列表 - 优化任务分配策略
    tasks = []

    # 策略：让每个GPU运行一个模型的所有type01实验
    # 这样可以减少模型加载次数，提高效率
    if gpu_count >= 2:
        # 2个GPU的情况：每个GPU运行一个模型的所有type01实验
        for i, model_info in enumerate(model_infos):
            gpu_id = i % gpu_count
            for type01 in [1, 2, 3]:
                tasks.append((model_info, gpu_id, type01))
    else:
        # 1个GPU的情况：顺序运行所有实验
        for model_info in model_infos:
            for type01 in [1, 2, 3]:
                tasks.append((model_info, 0, type01))

    print(f"总共 {len(tasks)} 个实验任务")
    for i, (model_info, gpu_id, type01) in enumerate(tasks):
        print(f"  任务 {i+1}: {model_info[1]} type01={type01} -> GPU {gpu_id}")

    start_time = datetime.now()
    print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 使用进程池管理并发执行的任务
    # 同时运行的进程数等于GPU数量
    with mp.Pool(processes=gpu_count) as pool:
        # starmap会把tasks列表中的每个元组解包作为run_experiment函数的参数
        pool.starmap(run_experiment, tasks)

    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\n========================================")
    print(f"所有实验完成！")
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {duration}")
    print(f"结果保存在 error/result-acc-ppl/ 目录下")
    print("========================================")