import os
import json
import torch
import multiprocessing as mp
from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight, error_inject_activation

# 确保结果目录存在
os.makedirs("error/result-acc-ppl", exist_ok=True)

def run_experiment(model_info, gpu_id, type01):
    """在指定GPU上运行单个模型的实验"""
    model_dir, model_name = model_info
    
    # 设置使用的GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
    print(f"进程 {os.getpid()} 在 GPU {gpu_id} 上运行 {model_name} 实验 (type01={type01})")
    
    # 实验参数
    error_rate_list = [0.0000000000001, 0.0000000000005, 0.000000000001, 0.000000000005, 
                       0.00000000001, 0.00000000005, 0.0000000001, 0.0000000005, 
                       0.000000001, 0.000000005, 0.00000001, 0.00000005, 
                       0.0000001, 0.0000005, 0.000001]
    
    # 根据type01确定bit_range_list
    if type01 == 3:
        bit_range_list = [2, 4, 6, 8, 10, 12, 14, 16]
    else:  # type01 = 1 或 2
        bit_range_list = [2, 4, 6, 8, 10, 12, 14, 15]
    
    # 结果保存路径
    save_path = f"error/result-acc-ppl/{model_name}_results_type{type01}.json"
    results = []
    
    print(f"开始加载模型 {model_name}...")
    model, tokenizer = load_llama_model_and_tokenizer(model_dir)
    
    for error_rate in error_rate_list:
        for bit_range in bit_range_list:
            print(f"{model_name} - type01={type01}, error_rate: {error_rate}, bit_range: {bit_range}")
            # 重新加载模型以确保每次实验都是从干净状态开始
            model, tokenizer = load_llama_model_and_tokenizer(model_dir)
            model = error_inject_weight(model, error_rate, bit_range, type01)
            hooks = error_inject_activation(model, error_rate, bit_range, type01)
            
            try:
                accuracy, ppl = eval_model(model, tokenizer)
                result_entry = {
                    "model": model_name,
                    "error_rate": error_rate,
                    "bit_range": bit_range,
                    "type": type01,
                    "acc": accuracy,
                    "ppl": ppl
                }
                results.append(result_entry)
                
                # 每次迭代后保存结果，防止中途中断丢失数据
                with open(save_path, "w", encoding="utf-8") as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"错误发生在 {model_name}, type01={type01}, error_rate={error_rate}, bit_range={bit_range}: {str(e)}")
            
            # 清理hooks
            for hook in hooks:
                hook.remove()
    
    print(f"{model_name} type01={type01} 实验完成！")

if __name__ == "__main__":
    # 为保证CUDA在多进程中的稳定性，建议使用'spawn'模式
    # 这行代码需要在任何与multiprocessing相关的代码之前
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass

    # 检查可用的GPU数量
    gpu_count = 2
    print(f"可用GPU数量: {gpu_count}")
    
    if gpu_count == 0:
        print("错误：没有可用的GPU。请检查您的环境。")
        exit()
    elif gpu_count < 4:
        print(f"警告: 只有 {gpu_count} 个GPU可用，少于4个模型并行所需数量。实验将轮流在可用GPU上运行。")
    
    # 模型信息: (模型路径, 模型名称)
    model_infos = [
        ("/root/lanyun-tmp/models/llama-3.2-1b", "llama-3.2-1b"),
        ("/root/lanyun-tmp/models/llama-3.2-3b", "llama-3.2-3b"),
        ("/root/lanyun-tmp/models/mistral-7b", "mistral-7b"),
        ("/root/lanyun-tmp/models/qwen2.5-3b", "qwen2.5-3b")
    ]
    
    # 创建任务列表
    tasks = []
    process_count = 0
    
    for model_info in model_infos:
        for type01 in [1, 2, 3]:
            # 如果GPU数量不足，则循环使用可用的GPU
            gpu_id = process_count % gpu_count
            tasks.append((model_info, gpu_id, type01))
            process_count += 1

    # 使用进程池管理并发执行的任务
    # Pool的processes参数指定了同时运行的最大进程数，这里设置为GPU的数量
    with mp.Pool(processes=gpu_count) as pool:
        # starmap会把tasks列表中的每个元组解包作为run_experiment函数的参数
        pool.starmap(run_experiment, tasks)
      
    print("所有实验完成！") 