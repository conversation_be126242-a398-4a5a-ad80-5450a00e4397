from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight, error_inject_activation
import json
import os



print("开始加载模型...")
model_dir = "/root/lanyun-tmp/models/llama-3.2-1b"
model_name = "llama-3.2-1b"
model, tokenizer = load_llama_model_and_tokenizer(model_dir)

error_rate_list=[0.0000000000001,0.0000000000005,0.000000000001,0.000000000005,0.00000000001,0.00000000005,0.0000000001,0.0000000005,0.000000001,0.000000005,0.00000001,0.00000005,0.0000001,0.0000005,0.000001]
bit_range_list  = [2,4,6,8,10,12,14,16]

type01=3

results = []
save_path = f"error/result-acc-ppl/{model_name}_results.json"



for error_rate in error_rate_list:
    for bit_range in bit_range_list:
        print(f"error_rate: {error_rate}, bit_range: {bit_range}")
        model, tokenizer = load_llama_model_and_tokenizer(model_dir)
        model = error_inject_weight(model, error_rate, bit_range, type01)
        hooks = error_inject_activation(model, error_rate, bit_range, type01)
        accuracy,ppl = eval_model(model, tokenizer)
        result_entry = {"error_rate": error_rate,"bit_range": bit_range,"acc": accuracy,"ppl": ppl}
        results.append(result_entry)
        with open(save_path, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

for hook in hooks:
    hook.remove()


# 每个实验都用四个模型，路径分别为：/root/lanyun-tmp/models/llama-3.2-1b，/root/lanyun-tmp/models/llama-3.2-3b，/root/lanyun-tmp/models/mistral-7b，/root/lanyun-tmp/models/qwen2.5-3b
# 实验一：type01=3，error_rate_list=[0.0000000000001,0.0000000000005,0.000000000001,0.000000000005,0.00000000001,0.00000000005,0.0000000001,0.0000000005,0.000000001,0.000000005,0.00000001,0.00000005,0.0000001,0.0000005,0.000001],bit_range_list  = [2,4,6,8,10,12,14,16]
#        type01=1，error_rate_list=[0.0000000000001,0.0000000000005,0.000000000001,0.000000000005,0.00000000001,0.00000000005,0.0000000001,0.0000000005,0.000000001,0.000000005,0.00000001,0.00000005,0.0000001,0.0000005,0.000001],bit_range_list  = [2,4,6,8,10,12,14,15]
#        type01=2，error_rate_list=[0.0000000000001,0.0000000000005,0.000000000001,0.000000000005,0.00000000001,0.00000000005,0.0000000001,0.0000000005,0.000000001,0.000000005,0.00000001,0.00000005,0.0000001,0.0000005,0.000001],bit_range_list  = [2,4,6,8,10,12,14,15]
# #观察3种模式下的准确率和困惑度


# 实验二：在推理中所有激活和权重的分布

# 实验三：注入错误数目固定的情况下，模型的准确率，error_number_list=[100，500，1000，3000，5000，8000]，bit_range=16，type01=3

# 实验四：观察翻转后的权重大小对结果的影响，interval_list=[[0,20],[20,40],[40,60],[60,80],[80,100]],error_rate=0.0000005,bit_range=16，type01=3 











