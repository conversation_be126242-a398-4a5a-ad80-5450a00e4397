#!/usr/bin/env python3
"""
测试并行设置是否正常工作
"""

import torch
import os
import multiprocessing as mp
from datetime import datetime

def test_gpu(gpu_id):
    """测试指定GPU是否可用"""
    os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
    
    try:
        device = torch.device("cuda:0")  # 在设置CUDA_VISIBLE_DEVICES后，总是使用cuda:0
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 进程 {os.getpid()} 测试 GPU {gpu_id}")
        
        # 创建一个简单的张量并移动到GPU
        x = torch.randn(1000, 1000).to(device)
        y = torch.randn(1000, 1000).to(device)
        
        # 执行一些计算
        for i in range(5):
            z = torch.matmul(x, y)
            print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: 计算 {i+1}/5 完成")
        
        # 检查GPU内存使用
        memory_allocated = torch.cuda.memory_allocated() / 1024**2  # MB
        print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: 内存使用 {memory_allocated:.2f} MB")
        
        # 清理
        del x, y, z
        torch.cuda.empty_cache()
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: 测试完成")
        return True
        
    except Exception as e:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] GPU {gpu_id}: 测试失败 - {str(e)}")
        return False

def main():
    """主函数"""
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass
    
    print("========================================")
    print("测试并行GPU设置")
    print("========================================")
    
    # 检查GPU数量
    gpu_count = torch.cuda.device_count()
    print(f"检测到GPU数量: {gpu_count}")
    
    if gpu_count == 0:
        print("错误：没有可用的GPU")
        return
    
    # 显示GPU信息
    for i in range(gpu_count):
        gpu_name = torch.cuda.get_device_name(i)
        print(f"GPU {i}: {gpu_name}")
    
    print("\n开始并行测试...")
    start_time = datetime.now()
    
    # 创建任务列表
    tasks = [(i,) for i in range(min(gpu_count, 2))]  # 最多测试2个GPU
    
    # 并行测试
    with mp.Pool(processes=len(tasks)) as pool:
        results = pool.starmap(test_gpu, tasks)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n========================================")
    print(f"测试完成！")
    print(f"耗时: {duration}")
    print(f"成功的GPU: {sum(results)}/{len(results)}")
    print("========================================")
    
    if all(results):
        print("✅ 所有GPU测试通过，可以开始并行实验！")
    else:
        print("❌ 部分GPU测试失败，请检查环境配置")

if __name__ == "__main__":
    main()
